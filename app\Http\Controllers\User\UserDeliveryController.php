<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Distribution;
use App\Models\DistributionItem;
use App\Models\WarehouseStock;
use App\Models\StockMovement;
use Illuminate\Http\Request;
use Carbon\Carbon;

class UserDeliveryController extends Controller
{
    /**
     * Display a listing of deliveries for the user's store
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $store = $user->store;

        if (!$store) {
            return redirect()->route('user.dashboard')->with('error', 'Anda belum terdaftar di toko manapun.');
        }

        $query = Distribution::where('store_id', $store->id)
            ->with(['product', 'store']);

        // Month filter - show all data by default, filter only if month is specified
        $filterMonth = $request->get('month');
        $startDate = null;
        $endDate = null;

        if ($filterMonth && $filterMonth !== 'all') {
            // Parse the filter month
            $startDate = Carbon::createFromFormat('Y-m', $filterMonth)->startOfMonth();
            $endDate = Carbon::createFromFormat('Y-m', $filterMonth)->endOfMonth();

            // Apply date filter only when month is selected
            $query->whereBetween('date_distributed', [$startDate, $endDate]);
        }

        // Apply status filter if provided (using confirmed boolean)
        if ($request->has('status') && $request->status) {
            if ($request->status === 'confirmed') {
                $query->where('confirmed', true);
            } elseif ($request->status === 'pending') {
                $query->where('confirmed', false);
            }
        }

        $deliveries = $query->orderBy('date_distributed', 'desc')
            ->paginate(10);

        // Load returns data for each delivery to calculate returnable quantities
        $deliveries->getCollection()->transform(function ($delivery) {
            // Get approved returns for this distribution
            $approvedReturns = \App\Models\ReturnModel::where('distribution_id', $delivery->id)
                ->where('status', 'approved')
                ->sum('quantity');

            // Calculate max returnable quantity (simplified: full quantity minus approved returns)
            $maxReturnable = max(0, $delivery->quantity - $approvedReturns);

            $delivery->approved_returns_quantity = $approvedReturns;
            $delivery->max_returnable_quantity = $maxReturnable;

            return $delivery;
        });

        // Get statistics - apply same filtering logic as main query
        $statsQuery = Distribution::where('store_id', $store->id);
        if ($filterMonth && $filterMonth !== 'all') {
            $statsQuery->whereBetween('date_distributed', [$startDate, $endDate]);
        }

        $stats = [
            'total' => (clone $statsQuery)->count(),
            'pending' => (clone $statsQuery)->where('confirmed', false)->count(),
            'confirmed' => (clone $statsQuery)->where('confirmed', true)->count(),
            'with_shortage' => (clone $statsQuery)->where('confirmed', true)
                ->whereColumn('received_quantity', '<', 'quantity')->count(),
        ];

        // Get filter options
        $statuses = [
            'pending' => 'Belum Dikonfirmasi',
            'confirmed' => 'Dikonfirmasi'
        ];

        // Get available months for filter dropdown
        $availableMonths = $this->getAvailableMonths($store->id);

        return view('user.deliveries', compact('deliveries', 'statuses', 'filterMonth', 'availableMonths', 'stats'));
    }

    /**
     * Display the specified delivery
     */
    public function show($id)
    {
        $user = auth()->user();
        $store = $user->store;

        if (!$store) {
            return redirect()->route('user.dashboard')->with('error', 'Anda belum terdaftar di toko manapun.');
        }

        $delivery = Distribution::where('store_id', $store->id)
            ->with(['product', 'store'])
            ->findOrFail($id);

        // Calculate returnable quantity for this delivery (simplified)
        $approvedReturns = \App\Models\ReturnModel::where('distribution_id', $delivery->id)
            ->where('status', 'approved')
            ->sum('quantity');

        $maxReturnable = max(0, $delivery->quantity - $approvedReturns);

        $delivery->approved_returns_quantity = $approvedReturns;
        $delivery->max_returnable_quantity = $maxReturnable;

        return view('user.deliveries.show', compact('delivery'));
    }

    /**
     * Note: Manual confirmation is no longer needed in the simplified system.
     * All distributions are auto-confirmed when created.
     * This method is kept for backward compatibility but will redirect to deliveries list.
     */
    public function confirm(Request $request, $id)
    {
        return redirect()->route('user.deliveries')
            ->with('info', 'Distribusi telah dikonfirmasi secara otomatis. Jika ada masalah, silakan buat permintaan retur.');
    }

    /**
     * Get available months for filter dropdown.
     */
    private function getAvailableMonths($storeId)
    {
        $months = [];

        // Add "All Time" option
        $months[] = [
            'value' => 'all',
            'label' => 'Semua Waktu'
        ];

        // Get months from existing distributions for this store
        $distributionMonths = Distribution::where('store_id', $storeId)
            ->selectRaw('DATE_FORMAT(date_distributed, "%Y-%m") as month')
            ->distinct()
            ->orderBy('month', 'desc')
            ->pluck('month');

        foreach ($distributionMonths as $month) {
            $date = Carbon::createFromFormat('Y-m', $month);
            $months[] = [
                'value' => $month,
                'label' => $date->format('F Y')
            ];
        }

        return $months;
    }
}
