<?php $__env->startSection('title', 'Detail Retur - Dashboard Toko'); ?>
<?php $__env->startSection('page-title', 'Detail Retur'); ?>

<?php $__env->startSection('content'); ?>
<div class="user-dashboard-returns-container">
    <!-- Header with Back Button -->
    <div class="user-dashboard-card">
        <div class="user-dashboard-card-header">
            <div class="user-dashboard-card-header-content">
                <div class="flex items-center space-x-3">
                    <a href="<?php echo e(route('user.returns.index')); ?>" class="user-dashboard-btn user-dashboard-btn-outline user-dashboard-btn-sm">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali
                    </a>
                    <div>
                        <h1 class="user-dashboard-card-title">Detail Retur #<?php echo e(substr($return->id, 0, 8)); ?></h1>
                        <p class="user-dashboard-card-subtitle">Informasi lengkap permintaan retur produk</p>
                    </div>
                </div>
            </div>
            <div class="user-dashboard-card-actions">
                <span class="user-dashboard-badge 
                    <?php if($return->status === 'requested'): ?> user-dashboard-badge-yellow
                    <?php elseif($return->status === 'approved'): ?> user-dashboard-badge-blue
                    <?php elseif($return->status === 'in_transit'): ?> user-dashboard-badge-purple
                    <?php elseif($return->status === 'completed'): ?> user-dashboard-badge-green
                    <?php elseif($return->status === 'rejected'): ?> user-dashboard-badge-red
                    <?php endif; ?>">
                    <?php echo e($return->status_in_indonesian); ?>

                </span>
            </div>
        </div>
    </div>

    <!-- Return Details -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Product Information -->
        <div class="user-dashboard-card">
            <div class="user-dashboard-card-header">
                <h2 class="user-dashboard-card-title">Informasi Produk</h2>
            </div>
            <div class="user-dashboard-card-content">
                <div class="space-y-4">
                    <div class="flex justify-between items-start">
                        <span class="text-sm font-medium text-gray-600">Nama Produk:</span>
                        <span class="text-sm text-gray-900 font-semibold text-right"><?php echo e($return->product->name ?? 'Produk tidak ditemukan'); ?></span>
                    </div>
                    <div class="flex justify-between items-start">
                        <span class="text-sm font-medium text-gray-600">Jumlah Retur:</span>
                        <span class="text-sm text-gray-900 font-semibold"><?php echo e(number_format($return->quantity)); ?> unit</span>
                    </div>
                    <div class="flex justify-between items-start">
                        <span class="text-sm font-medium text-gray-600">Alasan Retur:</span>
                        <span class="text-sm text-gray-900 font-semibold"><?php echo e($return->reason_in_indonesian); ?></span>
                    </div>
                    <?php if($return->supplier): ?>
                    <div class="flex justify-between items-start">
                        <span class="text-sm font-medium text-gray-600">Supplier:</span>
                        <span class="text-sm text-gray-900 font-semibold text-right"><?php echo e($return->supplier->name); ?></span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Return Information -->
        <div class="user-dashboard-card">
            <div class="user-dashboard-card-header">
                <h2 class="user-dashboard-card-title">Informasi Retur</h2>
            </div>
            <div class="user-dashboard-card-content">
                <div class="space-y-4">
                    <div class="flex justify-between items-start">
                        <span class="text-sm font-medium text-gray-600">Tanggal Retur:</span>
                        <span class="text-sm text-gray-900 font-semibold"><?php echo e($return->return_date->format('d/m/Y')); ?></span>
                    </div>
                    <div class="flex justify-between items-start">
                        <span class="text-sm font-medium text-gray-600">Diminta Oleh:</span>
                        <span class="text-sm text-gray-900 font-semibold text-right"><?php echo e($return->requestedBy->name ?? 'User tidak ditemukan'); ?></span>
                    </div>
                    <?php if($return->approved_date): ?>
                    <div class="flex justify-between items-start">
                        <span class="text-sm font-medium text-gray-600">Tanggal Disetujui:</span>
                        <span class="text-sm text-gray-900 font-semibold"><?php echo e($return->approved_date->format('d/m/Y')); ?></span>
                    </div>
                    <?php endif; ?>
                    <?php if($return->approvedBy): ?>
                    <div class="flex justify-between items-start">
                        <span class="text-sm font-medium text-gray-600">Disetujui Oleh:</span>
                        <span class="text-sm text-gray-900 font-semibold text-right"><?php echo e($return->approvedBy->name); ?></span>
                    </div>
                    <?php endif; ?>
                    <?php if($return->completed_date): ?>
                    <div class="flex justify-between items-start">
                        <span class="text-sm font-medium text-gray-600">Tanggal Selesai:</span>
                        <span class="text-sm text-gray-900 font-semibold"><?php echo e($return->completed_date->format('d/m/Y')); ?></span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Description -->
    <div class="user-dashboard-card">
        <div class="user-dashboard-card-header">
            <h2 class="user-dashboard-card-title">Deskripsi Masalah</h2>
        </div>
        <div class="user-dashboard-card-content">
            <p class="text-sm text-gray-700 leading-relaxed"><?php echo e($return->description ?: 'Tidak ada deskripsi.'); ?></p>
        </div>
    </div>

    <!-- Admin Notes (if any) -->
    <?php if($return->admin_notes): ?>
    <div class="user-dashboard-card">
        <div class="user-dashboard-card-header">
            <h2 class="user-dashboard-card-title">Catatan Admin</h2>
        </div>
        <div class="user-dashboard-card-content">
            <p class="text-sm text-gray-700 leading-relaxed"><?php echo e($return->admin_notes); ?></p>
        </div>
    </div>
    <?php endif; ?>

    <!-- Status Timeline -->
    <div class="user-dashboard-card">
        <div class="user-dashboard-card-header">
            <h2 class="user-dashboard-card-title">Status Timeline</h2>
        </div>
        <div class="user-dashboard-card-content">
            <div class="space-y-4">
                <!-- Requested -->
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900">Permintaan Dibuat</p>
                        <p class="text-xs text-gray-500"><?php echo e($return->created_at->format('d/m/Y H:i')); ?></p>
                    </div>
                </div>

                <?php if($return->approved_date): ?>
                <!-- Approved -->
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900">Disetujui Admin</p>
                        <p class="text-xs text-gray-500"><?php echo e($return->approved_date->format('d/m/Y H:i')); ?></p>
                    </div>
                </div>
                <?php endif; ?>

                <?php if($return->status === 'in_transit'): ?>
                <!-- In Transit -->
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900">Dalam Perjalanan</p>
                        <p class="text-xs text-gray-500">Produk sedang dalam proses retur</p>
                    </div>
                </div>
                <?php endif; ?>

                <?php if($return->completed_date): ?>
                <!-- Completed -->
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-2 h-2 bg-green-600 rounded-full mt-2"></div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900">Retur Selesai</p>
                        <p class="text-xs text-gray-500"><?php echo e($return->completed_date->format('d/m/Y H:i')); ?></p>
                    </div>
                </div>
                <?php endif; ?>

                <?php if($return->status === 'rejected'): ?>
                <!-- Rejected -->
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900">Permintaan Ditolak</p>
                        <p class="text-xs text-gray-500"><?php echo e($return->updated_at->format('d/m/Y H:i')); ?></p>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="user-dashboard-card">
        <div class="user-dashboard-card-content">
            <div class="flex justify-between items-center">
                <a href="<?php echo e(route('user.returns.index')); ?>" class="user-dashboard-btn user-dashboard-btn-outline">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Kembali ke Daftar Retur
                </a>
                
                <?php if($return->status === 'requested'): ?>
                <div class="text-sm text-gray-600">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Menunggu persetujuan admin
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/user/returns/show.blade.php ENDPATH**/ ?>