<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ReturnModel;
use App\Models\Product;
use App\Models\Store;
use App\Models\Supplier;
use App\Models\StoreStock;
use App\Models\StockMovement;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AdminStoreReturnController extends Controller
{
    /**
     * Display a listing of store-to-warehouse returns.
     */
    public function index(Request $request)
    {
        $query = ReturnModel::with(['product', 'store', 'supplier', 'requestedBy', 'approvedBy'])
            ->active() // Only show active returns (not moved to history)
            ->storeToWarehouse(); // Only store-to-warehouse returns
        
        // Month filter - show all data by default, filter only if month is specified
        $filterMonth = $request->get('month');
        $startDate = null;
        $endDate = null;

        if ($filterMonth && $filterMonth !== 'all') {
            // Parse the filter month
            $startDate = Carbon::createFromFormat('Y-m', $filterMonth)->startOfMonth();
            $endDate = Carbon::createFromFormat('Y-m', $filterMonth)->endOfMonth();

            // Apply date filter only when month is selected
            $query->whereBetween('return_date', [$startDate, $endDate]);
        }
        
        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->whereHas('product', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            })->orWhereHas('store', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }
        
        // Apply status filter
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }
        
        // Apply store filter
        if ($request->filled('store_id')) {
            $query->where('store_id', $request->get('store_id'));
        }
        
        $returns = $query->orderBy('return_date', 'desc')->paginate(10);
        
        // Get statistics - apply same filtering logic as main query
        $statsQuery = ReturnModel::active()->storeToWarehouse();
        if ($filterMonth && $filterMonth !== 'all') {
            $statsQuery->whereBetween('return_date', [$startDate, $endDate]);
        }

        $stats = [
            'total' => (clone $statsQuery)->count(),
            'requested' => (clone $statsQuery)->where('status', 'requested')->count(),
            'approved' => (clone $statsQuery)->where('status', 'approved')->count(),
            'completed' => (clone $statsQuery)->where('status', 'completed')->count(),
            'forwarded_to_supplier' => (clone $statsQuery)->whereNotNull('supplier_id')
                ->where('status', 'in_transit')->count(),
        ];

        // Get stores for filter dropdown
        $stores = Store::orderBy('name')->get();

        // Get available months for filter dropdown
        $availableMonths = $this->getAvailableMonths();

        return view('admin.store-returns.index', compact('returns', 'stats', 'filterMonth', 'stores', 'availableMonths'));
    }
    
    /**
     * Display the specified store return.
     */
    public function show(ReturnModel $storeReturn)
    {
        // Ensure this is a store return
        if (!$storeReturn->isFromStore()) {
            abort(404, 'Retur tidak ditemukan');
        }
        
        $storeReturn->load(['product', 'store', 'supplier', 'requestedBy', 'approvedBy']);
        
        return view('admin.store-returns.show', compact('storeReturn'));
    }
    
    /**
     * Approve a store return request.
     */
    public function approve(Request $request, ReturnModel $storeReturn)
    {
        // Ensure this is a store return and status is requested
        if (!$storeReturn->isFromStore() || $storeReturn->status !== 'requested') {
            return redirect()->route('admin.store-returns.index')
                ->with('error', 'Retur ini sudah diproses sebelumnya');
        }
        
        $validatedData = $request->validate([
            'admin_notes' => 'nullable|string|max:1000',
        ], [
            'admin_notes.max' => 'Catatan maksimal 1000 karakter',
        ]);
        
        DB::transaction(function () use ($storeReturn, $validatedData) {
            $storeReturn->update([
                'status' => 'approved',
                'approved_date' => now(),
                'approved_by' => auth()->id(),
                'admin_notes' => $validatedData['admin_notes'],
            ]);

            // Option B: Increase warehouse stock when approving store returns
            $warehouseStock = \App\Models\WarehouseStock::where('product_id', $storeReturn->product_id)
                ->whereNull('store_id') // Central warehouse stock only
                ->first();

            if (!$warehouseStock) {
                // Create new warehouse stock entry if it doesn't exist
                $warehouseStock = \App\Models\WarehouseStock::create([
                    'product_id' => $storeReturn->product_id,
                    'quantity' => $storeReturn->quantity,
                    'store_id' => null,
                    'date_received' => now()->toDateString(),
                ]);
            } else {
                $warehouseStock->increment('quantity', $storeReturn->quantity);
            }

            // Record stock movement for warehouse increase
            \App\Models\StockMovement::create([
                'product_id' => $storeReturn->product_id,
                'type' => 'in',
                'source' => 'store_adjustment',
                'quantity' => $storeReturn->quantity,
                'previous_stock' => $warehouseStock->quantity - $storeReturn->quantity,
                'new_stock' => $warehouseStock->quantity,
                'reference_type' => 'ReturnModel',
                'reference_id' => $storeReturn->id,
                'notes' => 'Retur dari toko disetujui: ' . $storeReturn->store->name,
                'created_by' => auth()->id(),
            ]);

            // Reduce store stock when warehouse admin approves the return
            $storeStock = \App\Models\StoreStock::where('store_id', $storeReturn->store_id)
                ->where('product_id', $storeReturn->product_id)
                ->first();

            if ($storeStock && $storeStock->quantity >= $storeReturn->quantity) {
                $storeStock->decrement('quantity', $storeReturn->quantity);

                // Record store stock movement
                \App\Models\StockMovement::create([
                    'product_id' => $storeReturn->product_id,
                    'type' => 'out',
                    'source' => 'store_adjustment',
                    'quantity' => -$storeReturn->quantity,
                    'previous_stock' => $storeStock->quantity + $storeReturn->quantity,
                    'new_stock' => $storeStock->quantity,
                    'reference_type' => 'ReturnModel',
                    'reference_id' => $storeReturn->id,
                    'notes' => 'Stok toko dikurangi karena retur disetujui: ' . $storeReturn->store->name,
                    'created_by' => auth()->id(),
                ]);
            }
        });

        return redirect()->route('admin.store-returns.index')
            ->with('success', 'Retur dari toko berhasil disetujui');
    }
    
    /**
     * Reject a store return request.
     */
    public function reject(Request $request, ReturnModel $storeReturn)
    {
        // Ensure this is a store return and status is requested
        if (!$storeReturn->isFromStore() || $storeReturn->status !== 'requested') {
            return redirect()->route('admin.store-returns.index')
                ->with('error', 'Retur ini sudah diproses sebelumnya');
        }
        
        $validatedData = $request->validate([
            'admin_notes' => 'required|string|max:1000',
        ], [
            'admin_notes.required' => 'Alasan penolakan wajib diisi',
            'admin_notes.max' => 'Catatan maksimal 1000 karakter',
        ]);
        
        $storeReturn->update([
            'status' => 'rejected',
            'approved_by' => auth()->id(),
            'admin_notes' => $validatedData['admin_notes'],
        ]);

        // Move to history when rejected
        $storeReturn->moveToHistory('rejected_final', $validatedData['admin_notes'], auth()->id());

        return redirect()->route('admin.store-returns.index')
            ->with('success', 'Retur dari toko berhasil ditolak');
    }
    
    /**
     * Forward a store return to supplier.
     */
    public function forwardToSupplier(Request $request, ReturnModel $storeReturn)
    {
        // Only allow forwarding approved store returns
        if (!$storeReturn->isFromStore() || $storeReturn->status !== 'approved') {
            return redirect()->route('admin.store-returns.index')
                ->with('error', 'Retur ini tidak dapat diteruskan ke supplier');
        }
        
        $validatedData = $request->validate([
            'supplier_id' => 'required|exists:suppliers,id',
            'admin_notes' => 'nullable|string|max:1000',
        ], [
            'supplier_id.required' => 'Supplier wajib dipilih',
            'supplier_id.exists' => 'Supplier tidak valid',
            'admin_notes.max' => 'Catatan maksimal 1000 karakter',
        ]);
        
        DB::transaction(function () use ($storeReturn, $validatedData) {
            // Keep the original user return status as 'approved' - do not change it
            // Instead, create a separate warehouse-to-supplier return record
            $warehouseReturn = ReturnModel::create([
                'product_id' => $storeReturn->product_id,
                'store_id' => null, // Warehouse return (no store_id)
                'supplier_id' => $validatedData['supplier_id'],
                'quantity' => $storeReturn->quantity,
                'reason' => $storeReturn->reason,
                'description' => $storeReturn->description,
                'status' => 'requested', // New warehouse return starts as requested
                'return_date' => now(),
                'requested_by' => auth()->id(),
                'admin_notes' => 'Forwarded from store return: ' . ($validatedData['admin_notes'] ?? 'Diteruskan ke supplier'),
            ]);

            // Update the original store return to indicate it has been forwarded
            // Set supplier_id to mark it as forwarded (this will exclude it from store-returns page)
            // but keep status as 'approved' so smart return logic continues to work for users
            $storeReturn->update([
                'supplier_id' => $validatedData['supplier_id'], // Mark as forwarded to supplier
                'admin_notes' => ($storeReturn->admin_notes ?? '') . "\n\nForwarded to supplier: " . ($validatedData['admin_notes'] ?? 'Diteruskan ke supplier'),
            ]);

            // Fixed: Do not reduce warehouse stock when forwarding to supplier
            // Stock will only be reduced when supplier actually approves the return
            // This prevents double stock deduction bug

            // Record the forwarding action in stock movements for audit trail only
            \App\Models\StockMovement::create([
                'product_id' => $storeReturn->product_id,
                'type' => 'adjustment',
                'source' => 'adjustment', // Use existing ENUM value for audit trail
                'quantity' => 0, // No actual stock change, just audit trail
                'previous_stock' => 0,
                'new_stock' => 0,
                'reference_type' => 'ReturnModel',
                'reference_id' => $warehouseReturn->id,
                'notes' => 'Retur toko diteruskan ke supplier: ' . $storeReturn->store->name,
                'created_by' => auth()->id(),
            ]);
        });
        
        return redirect()->route('admin.store-returns.index')
            ->with('success', 'Retur dari toko berhasil diteruskan ke supplier');
    }
    
    /**
     * Complete a store return (for returns not forwarded to supplier).
     */
    public function complete(Request $request, ReturnModel $storeReturn)
    {
        // Only allow completing approved store returns that are not forwarded to supplier
        if (!$storeReturn->isFromStore() || !in_array($storeReturn->status, ['approved', 'in_transit'])) {
            return redirect()->route('admin.store-returns.index')
                ->with('error', 'Retur ini tidak dapat diselesaikan');
        }
        
        $validatedData = $request->validate([
            'admin_notes' => 'nullable|string|max:1000',
        ], [
            'admin_notes.max' => 'Catatan maksimal 1000 karakter',
        ]);
        
        DB::transaction(function () use ($storeReturn, $validatedData) {
            // Update return status
            $storeReturn->update([
                'status' => 'completed',
                'completed_date' => now(),
                'admin_notes' => ($storeReturn->admin_notes ?? '') . "\n\nCompleted: " . ($validatedData['admin_notes'] ?? 'Retur selesai'),
            ]);

            // Move to history after completion
            $storeReturn->moveToHistory('accepted', $validatedData['admin_notes'] ?? 'Retur toko diselesaikan oleh admin', auth()->id());

            // Note: Store stock was already reduced when the return was approved
            // No additional stock adjustments needed here
        });
        
        return redirect()->route('admin.store-returns.index')
            ->with('success', 'Retur dari toko berhasil diselesaikan');
    }

    /**
     * Get the most recent supplier for a product (API endpoint).
     */
    public function getRecentSupplier(Product $product)
    {
        $recentDelivery = $product->supplierDeliveries()
            ->with('supplier')
            ->where('status', 'received')
            ->orderBy('received_date', 'desc')
            ->first();

        if ($recentDelivery) {
            return response()->json([
                'supplier' => [
                    'id' => $recentDelivery->supplier->id,
                    'name' => $recentDelivery->supplier->name,
                ],
                'last_delivery_date' => $recentDelivery->received_date->format('d/m/Y'),
            ]);
        }

        return response()->json(['supplier' => null]);
    }

    /**
     * Get available months for filter dropdown.
     */
    private function getAvailableMonths()
    {
        $months = [];

        // Add "All Time" option
        $months[] = [
            'value' => 'all',
            'label' => 'Semua Waktu'
        ];

        // Get months from existing store returns
        $returnMonths = ReturnModel::active()
            ->storeToWarehouse()
            ->selectRaw('DATE_FORMAT(return_date, "%Y-%m") as month')
            ->distinct()
            ->orderBy('month', 'desc')
            ->pluck('month');

        foreach ($returnMonths as $month) {
            $date = Carbon::createFromFormat('Y-m', $month);
            $months[] = [
                'value' => $month,
                'label' => $date->format('F Y')
            ];
        }

        return $months;
    }
}
